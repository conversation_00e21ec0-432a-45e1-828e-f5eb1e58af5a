// 利率類型
export type MortgageRateType = 'fixed' | 'floating';

// 自備款輸入方式
export type DownPaymentType = 'amount' | 'percentage';

// 房貸輸入參數
export interface MortgageInput {
  housePrice: number; // 房屋總價
  downPaymentType: DownPaymentType; // 自備款輸入方式
  downPaymentAmount?: number; // 自備款金額
  downPaymentPercentage?: number; // 自備款百分比
  loanAmount: number; // 貸款金額 (自動計算)
  loanYears: number; // 貸款年限
  rateType: MortgageRateType; // 利率類型
  interestRate: number; // 年利率 (%)
  graceYears: number; // 寬限期 (年)

  // 相關費用
  fees: {
    notaryFee: number; // 代書費
    registrationFeeRate: number; // 規費比例 (%)
    fireInsuranceAnnual: number; // 火險費 (年費)
    earthquakeInsuranceAnnual: number; // 地震險 (年費)
    appraisalFee: number; // 鑑價費
  };
}

// 每月還款明細
export interface MortgageMonthlyPayment {
  month: number; // 期數
  interest: number; // 本期應繳利息
  principal: number; // 本期應還本金
  totalPayment: number; // 本息合計
  remainingBalance: number; // 剩餘本金
  isGracePeriod: boolean; // 是否為寬限期
}

// 房貸計算結果
export interface MortgageResult {
  monthlyPayments: MortgageMonthlyPayment[]; // 還款計劃
  summary: {
    monthlyPaymentNormal: number; // 正常期月付金
    monthlyPaymentGrace: number; // 寬限期月付金
    totalInterest: number; // 總利息
    totalPrincipal: number; // 總本金
    totalFees: number; // 總費用
    totalAmount: number; // 總還款金額
    interestRatio: number; // 利息佔總還款比例 (%)
  };

  // 圖表數據
  chartData: {
    principalVsInterest: {
      principal: number;
      interest: number;
    };
    cumulativePayments: Array<{
      month: number;
      cumulativePrincipal: number;
      cumulativeInterest: number;
      cumulativeTotal: number;
    }>;
  };
}

// 表單驗證錯誤
export interface MortgageFormErrors {
  housePrice?: string;
  downPaymentAmount?: string;
  downPaymentPercentage?: string;
  loanYears?: string;
  interestRate?: string;
  graceYears?: string;
  fees?: {
    notaryFee?: string;
    registrationFeeRate?: string;
    fireInsuranceAnnual?: string;
    earthquakeInsuranceAnnual?: string;
    appraisalFee?: string;
  };
}