import { MortgageInput, MortgageResult, MortgageMonthlyPayment } from '../types/mortgage';

/**
 * 計算每月利率
 * @param annualRate 年利率 (%)
 * @returns 月利率 (小數)
 */
function getMonthlyRate(annualRate: number): number {
  return annualRate / 100 / 12;
}

/**
 * 計算等額本息還款的月付金
 * @param principal 本金
 * @param monthlyRate 月利率
 * @param months 期數
 * @returns 月付金
 */
function calculateEqualPayment(principal: number, monthlyRate: number, months: number): number {
  if (monthlyRate === 0) {
    return principal / months;
  }
  
  const factor = Math.pow(1 + monthlyRate, months);
  return principal * (monthlyRate * factor) / (factor - 1);
}

/**
 * 計算房貸還款計劃
 * @param input 房貸輸入參數
 * @returns 房貸計算結果
 */
export function calculateMortgage(input: MortgageInput): MortgageResult {
  const monthlyPayments: MortgageMonthlyPayment[] = [];
  const monthlyRate = getMonthlyRate(input.interestRate);
  const totalMonths = input.loanYears * 12;
  const graceMonths = input.graceYears * 12;
  const normalMonths = totalMonths - graceMonths;
  
  let remainingBalance = input.loanAmount;
  
  // 計算正常期月付金 (本息攤還)
  const monthlyPaymentNormal = calculateEqualPayment(input.loanAmount, monthlyRate, normalMonths);
  
  // 計算寬限期月付金 (只繳利息)
  const monthlyPaymentGrace = input.loanAmount * monthlyRate;
  
  // 計算每月還款明細
  for (let month = 1; month <= totalMonths; month++) {
    const isGracePeriod = month <= graceMonths;
    
    // 計算當月利息
    const interest = remainingBalance * monthlyRate;
    
    let principal: number;
    let totalPayment: number;
    
    if (isGracePeriod) {
      // 寬限期：只繳利息
      principal = 0;
      totalPayment = interest;
    } else {
      // 正常期：本息攤還
      if (month === totalMonths) {
        // 最後一期，還清剩餘本金
        principal = remainingBalance;
        totalPayment = principal + interest;
      } else {
        // 使用等額本息公式
        const remainingNormalMonths = totalMonths - month + 1;
        const currentMonthlyPayment = calculateEqualPayment(remainingBalance, monthlyRate, remainingNormalMonths);
        principal = currentMonthlyPayment - interest;
        totalPayment = currentMonthlyPayment;
        
        // 確保本金不為負數
        if (principal < 0) {
          principal = 0;
          totalPayment = interest;
        }
        
        // 確保本金不超過剩餘本金
        if (principal > remainingBalance) {
          principal = remainingBalance;
          totalPayment = principal + interest;
        }
      }
    }
    
    monthlyPayments.push({
      month,
      interest,
      principal,
      totalPayment,
      remainingBalance: remainingBalance - principal,
      isGracePeriod
    });
    
    remainingBalance -= principal;
  }
  
  // 計算總計
  const totalInterest = monthlyPayments.reduce((sum, payment) => sum + payment.interest, 0);
  const totalPrincipal = input.loanAmount;
  
  // 計算總費用
  const registrationFee = input.loanAmount * (input.fees.registrationFeeRate / 100);
  const totalInsuranceFees = (input.fees.fireInsuranceAnnual + input.fees.earthquakeInsuranceAnnual) * input.loanYears;
  const totalFees = input.fees.notaryFee + registrationFee + totalInsuranceFees + input.fees.appraisalFee;
  
  const totalAmount = totalPrincipal + totalInterest + totalFees;
  const interestRatio = (totalInterest / totalAmount) * 100;
  
  // 準備圖表數據
  const chartData = {
    principalVsInterest: {
      principal: totalPrincipal,
      interest: totalInterest
    },
    cumulativePayments: monthlyPayments.map((payment, index) => {
      const cumulativePrincipal = monthlyPayments
        .slice(0, index + 1)
        .reduce((sum, p) => sum + p.principal, 0);
      const cumulativeInterest = monthlyPayments
        .slice(0, index + 1)
        .reduce((sum, p) => sum + p.interest, 0);
      
      return {
        month: payment.month,
        cumulativePrincipal,
        cumulativeInterest,
        cumulativeTotal: cumulativePrincipal + cumulativeInterest
      };
    })
  };
  
  return {
    monthlyPayments,
    summary: {
      monthlyPaymentNormal,
      monthlyPaymentGrace,
      totalInterest,
      totalPrincipal,
      totalFees,
      totalAmount,
      interestRatio
    },
    chartData
  };
}

/**
 * 計算自備款金額
 * @param housePrice 房屋總價
 * @param downPaymentType 自備款類型
 * @param downPaymentAmount 自備款金額
 * @param downPaymentPercentage 自備款百分比
 * @returns 自備款金額
 */
export function calculateDownPayment(
  housePrice: number,
  downPaymentType: 'amount' | 'percentage',
  downPaymentAmount?: number,
  downPaymentPercentage?: number
): number {
  if (downPaymentType === 'amount') {
    return downPaymentAmount || 0;
  } else {
    return housePrice * ((downPaymentPercentage || 0) / 100);
  }
}

/**
 * 驗證房貸輸入參數
 * @param input 房貸輸入參數
 * @returns 驗證錯誤訊息
 */
export function validateMortgageInput(input: Partial<MortgageInput>): string[] {
  const errors: string[] = [];
  
  if (!input.housePrice || input.housePrice <= 0) {
    errors.push('房屋總價必須大於 0');
  }
  
  if (input.downPaymentType === 'amount') {
    if (!input.downPaymentAmount || input.downPaymentAmount < 0) {
      errors.push('自備款金額不能為負數');
    }
    if (input.housePrice && input.downPaymentAmount && input.downPaymentAmount >= input.housePrice) {
      errors.push('自備款不能大於等於房屋總價');
    }
  } else if (input.downPaymentType === 'percentage') {
    if (input.downPaymentPercentage === undefined || input.downPaymentPercentage < 0 || input.downPaymentPercentage >= 100) {
      errors.push('自備款百分比必須在 0-99% 之間');
    }
  }
  
  if (!input.loanYears || input.loanYears <= 0 || input.loanYears > 40) {
    errors.push('貸款年限必須在 1-40 年之間');
  }
  
  if (!input.interestRate || input.interestRate <= 0 || input.interestRate > 30) {
    errors.push('年利率必須在 0-30% 之間');
  }
  
  if (input.graceYears !== undefined && (input.graceYears < 0 || input.graceYears > 5)) {
    errors.push('寬限期必須在 0-5 年之間');
  }
  
  if (input.loanYears && input.graceYears && input.graceYears >= input.loanYears) {
    errors.push('寬限期不能大於等於貸款年限');
  }
  
  // 驗證費用
  if (input.fees) {
    if (input.fees.notaryFee < 0) {
      errors.push('代書費不能為負數');
    }
    if (input.fees.registrationFeeRate < 0 || input.fees.registrationFeeRate > 10) {
      errors.push('規費比例必須在 0-10% 之間');
    }
    if (input.fees.fireInsuranceAnnual < 0) {
      errors.push('火險費不能為負數');
    }
    if (input.fees.earthquakeInsuranceAnnual < 0) {
      errors.push('地震險不能為負數');
    }
    if (input.fees.appraisalFee < 0) {
      errors.push('鑑價費不能為負數');
    }
  }
  
  return errors;
}

/**
 * 計算貸款成數
 * @param housePrice 房屋總價
 * @param loanAmount 貸款金額
 * @returns 貸款成數 (%)
 */
export function calculateLoanToValueRatio(housePrice: number, loanAmount: number): number {
  if (housePrice === 0) return 0;
  return (loanAmount / housePrice) * 100;
}

/**
 * 計算月收入建議
 * @param monthlyPayment 月付金
 * @param debtRatio 負債比 (預設 1/3)
 * @returns 建議月收入
 */
export function calculateRecommendedIncome(monthlyPayment: number, debtRatio: number = 1/3): number {
  return monthlyPayment / debtRatio;
}
