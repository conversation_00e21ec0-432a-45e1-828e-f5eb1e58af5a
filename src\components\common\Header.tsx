'use client';

import { Menubar } from 'primereact/menubar';
import { Button } from 'primereact/button';
import { MenuItem } from 'primereact/menuitem';
import { useRouter } from 'next/navigation';

export default function Header() {
  const router = useRouter();

  const items: MenuItem[] = [
    {
      label: '首頁',
      icon: 'pi pi-home',
      command: () => router.push('/')
    },
    {
      label: '信貸計算',
      icon: 'pi pi-calculator',
      command: () => router.push('/credit-loan')
    },
    {
      label: '房貸計算',
      icon: 'pi pi-building',
      command: () => router.push('/mortgage')
    }
  ];

  const start = (
    <div className="flex items-center gap-2">
      <i className="pi pi-calculator text-2xl text-blue-600"></i>
      <span className="font-bold text-xl text-gray-800">貸款計算機</span>
    </div>
  );

  const end = (
    <Button 
      icon="pi pi-cog" 
      className="p-button-text" 
      tooltip="設定"
      tooltipOptions={{ position: 'bottom' }}
    />
  );

  return (
    <div className="bg-white shadow-sm border-b">
      <Menubar 
        model={items} 
        start={start} 
        end={end}
        className="border-none bg-transparent"
      />
    </div>
  );
}