// 利率計算方式
export type InterestRateType = 'single' | 'two-stage' | 'three-stage';

// 信貸輸入參數
export interface CreditLoanInput {
  loanAmount: number; // 貸款金額
  termMonths: number; // 期數(月)
  rateType: InterestRateType; // 利率計算方式

  // 單一利率
  singleRate?: number;

  // 2段式利率
  twoStageRates?: {
    firstStage: {
      months: number;
      rate: number;
    };
    secondStage: {
      rate: number;
    };
  };

  // 3段式利率
  threeStageRates?: {
    firstStage: {
      months: number;
      rate: number;
    };
    secondStage: {
      months: number;
      rate: number;
    };
    thirdStage: {
      rate: number;
    };
  };

  // 相關費用
  fees: {
    setupFee: number; // 開辦費
    monthlyFee: number; // 帳管費(每月)
    otherFees: number; // 其他費用(一次性)
  };
}

// 每月還款明細
export interface MonthlyPayment {
  month: number; // 期數
  interest: number; // 本期應繳利息
  principal: number; // 本期應還本金
  totalPayment: number; // 本息合計
  remainingBalance: number; // 剩餘本金
  monthlyFee: number; // 帳管費
  totalMonthlyPayment: number; // 含費用的總月付金
}

// 信貸計算結果
export interface CreditLoanResult {
  monthlyPayments: MonthlyPayment[]; // 還款計劃
  summary: {
    totalInterest: number; // 總利息
    totalPrincipal: number; // 總本金
    totalFees: number; // 總費用
    totalAmount: number; // 總還款金額
    effectiveAnnualRate: number; // 實際年利率 (APR)
    averageMonthlyPayment: number; // 平均月付金
  };
}

// 表單驗證錯誤
export interface CreditLoanFormErrors {
  loanAmount?: string;
  termMonths?: string;
  singleRate?: string;
  twoStageRates?: {
    firstStage?: {
      months?: string;
      rate?: string;
    };
    secondStage?: {
      rate?: string;
    };
  };
  threeStageRates?: {
    firstStage?: {
      months?: string;
      rate?: string;
    };
    secondStage?: {
      months?: string;
      rate?: string;
    };
    thirdStage?: {
      rate?: string;
    };
  };
  fees?: {
    setupFee?: string;
    monthlyFee?: string;
    otherFees?: string;
  };
}