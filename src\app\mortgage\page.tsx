'use client';

import { useState } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import MortgageForm from '@/components/mortgage/MortgageForm';
import MortgageSummary from '@/components/mortgage/MortgageSummary';
import MortgageChart from '@/components/mortgage/MortgageChart';
import MortgageTable from '@/components/mortgage/MortgageTable';
import { MortgageInput, MortgageResult } from '@/lib/types/mortgage';
import { calculateMortgage } from '@/lib/calculations/mortgage';

export default function MortgagePage() {
  const [result, setResult] = useState<MortgageResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleCalculate = async (input: MortgageInput) => {
    setLoading(true);
    try {
      // 模擬計算延遲
      await new Promise(resolve => setTimeout(resolve, 500));

      const calculationResult = calculateMortgage(input);
      setResult(calculationResult);
    } catch (error) {
      console.error('計算錯誤:', error);
      // 這裡可以添加錯誤處理
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">房貸計算</h1>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            {/* 表單區域 */}
            <div>
              <h2 className="text-xl font-semibold mb-6">房貸資訊</h2>
              <MortgageForm onCalculate={handleCalculate} loading={loading} />
            </div>

            {/* 結果區域 */}
            <div>
              <h2 className="text-xl font-semibold mb-6">計算結果</h2>
              <TabView>
                <TabPanel header="統計摘要" leftIcon="pi pi-chart-bar mr-2">
                  <MortgageSummary result={result} loading={loading} />
                </TabPanel>
                <TabPanel header="圖表分析" leftIcon="pi pi-chart-pie mr-2">
                  <MortgageChart result={result} loading={loading} />
                </TabPanel>
              </TabView>
            </div>
          </div>

          {/* 還款計劃表 */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-6">還款計劃表</h2>
            <MortgageTable
              payments={result?.monthlyPayments || []}
              loading={loading}
            />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}